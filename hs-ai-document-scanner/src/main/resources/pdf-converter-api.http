### PDF Converter API Test Examples
### Base URL: http://localhost:34001/v3/ocr

### 1. Get service information
GET http://localhost:34001/v3/ocr/api/pdf-converter/info
Accept: application/json

### 2. Convert PDF to Scanned PDF (default 300 DPI)
POST http://localhost:34001/v3/ocr/api/pdf-converter/convert-to-scanned
Content-Type: multipart/form-data; boundary=boundary

--boundary
Content-Disposition: form-data; name="file"; filename="sample.pdf"
Content-Type: application/pdf

< ./sample/sample-document.pdf
--boundary--

### 3. Convert PDF to Scanned PDF with custom DPI (150 DPI - smaller file)
POST http://localhost:34001/v3/ocr/api/pdf-converter/convert-to-scanned
Content-Type: multipart/form-data; boundary=boundary

--boundary
Content-Disposition: form-data; name="file"; filename="sample.pdf"
Content-Type: application/pdf

< ./sample/sample-document.pdf
--boundary
Content-Disposition: form-data; name="dpi"

150
--boundary--

### 4. Convert PDF to Scanned PDF with high DPI (600 DPI - larger file, better quality)
POST http://localhost:34001/v3/ocr/api/pdf-converter/convert-to-scanned
Content-Type: multipart/form-data; boundary=boundary

--boundary
Content-Disposition: form-data; name="file"; filename="sample.pdf"
Content-Type: application/pdf

< ./sample/sample-document.pdf
--boundary
Content-Disposition: form-data; name="dpi"

600
--boundary--

### 5. Test with invalid DPI (should return 400 Bad Request)
POST http://localhost:34001/v3/ocr/api/pdf-converter/convert-to-scanned
Content-Type: multipart/form-data; boundary=boundary

--boundary
Content-Disposition: form-data; name="file"; filename="sample.pdf"
Content-Type: application/pdf

< ./sample/sample-document.pdf
--boundary
Content-Disposition: form-data; name="dpi"

100
--boundary--

### 6. Test with non-PDF file (should return 400 Bad Request)
POST http://localhost:34001/v3/ocr/api/pdf-converter/convert-to-scanned
Content-Type: multipart/form-data; boundary=boundary

--boundary
Content-Disposition: form-data; name="file"; filename="document.txt"
Content-Type: text/plain

This is not a PDF file content
--boundary--

### 7. Convert using curl command (alternative)
# curl -X POST \
#   http://localhost:34001/v3/ocr/api/pdf-converter/convert-to-scanned \
#   -H 'Content-Type: multipart/form-data' \
#   -F 'file=@./sample/sample-document.pdf' \
#   -F 'dpi=300' \
#   --output scanned_output.pdf

### 8. Convert with custom DPI using curl
# curl -X POST \
#   http://localhost:34001/v3/ocr/api/pdf-converter/convert-to-scanned \
#   -H 'Content-Type: multipart/form-data' \
#   -F 'file=@./sample/sample-document.pdf' \
#   -F 'dpi=600' \
#   --output high_quality_scanned.pdf \
#   -v

### 9. Get service info using curl
# curl -X GET \
#   http://localhost:34001/v3/ocr/api/pdf-converter/info \
#   -H 'Accept: application/json' \
#   | jq .

###
### Expected Response Headers for successful conversion:
### Content-Type: application/pdf
### Content-Disposition: attachment; filename="scanned_[original-filename].pdf"
### X-Processing-Time-Ms: [processing time in milliseconds]
### X-Original-Size-Bytes: [original file size]
### X-Output-Size-Bytes: [converted file size]
### X-DPI: [DPI used for conversion]
###

###
### Expected Response for service info:
### {
###   "service": "PDF to Scanned PDF Converter",
###   "description": "Converts regular PDF documents into 'scanned' PDFs where each page is rendered as an image",
###   "version": "1.0.0",
###   "supportedFormats": {
###     "input": "PDF (application/pdf)",
###     "output": "PDF (application/pdf) with image-based pages"
###   },
###   "dpiSettings": {
###     "minimum": 150,
###     "default": 300,
###     "recommended": 300,
###     "maximum": 600,
###     "description": "Higher DPI = better quality but larger file size"
###   },
###   "useCases": [
###     "Testing OCR services with scanned documents",
###     "Document processing workflows expecting image-based PDFs",
###     "Simulating physical document scanning",
###     "Converting text-based PDFs to image-based PDFs for security"
###   ],
###   "endpoints": {
###     "convert": "/api/pdf-converter/convert-to-scanned",
###     "info": "/api/pdf-converter/info"
###   }
### }
###

###
### Error Response Examples:
###

### Invalid DPI (< 150):
### {
###   "error": "Invalid input: DPI must be at least 150 for readable output",
###   "timestamp": "2025-08-14T23:30:00.000Z"
### }

### Invalid file type:
### {
###   "error": "Invalid input: File must be a PDF document. Received content type: text/plain",
###   "timestamp": "2025-08-14T23:30:00.000Z"
### }

### Empty file:
### {
###   "error": "Invalid input: File is empty",
###   "timestamp": "2025-08-14T23:30:00.000Z"
### }

### Processing error:
### {
###   "error": "Failed to process PDF: [specific error message]",
###   "timestamp": "2025-08-14T23:30:00.000Z"
### }
###
