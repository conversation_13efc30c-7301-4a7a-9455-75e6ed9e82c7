package com.vitality.ai.ocr.service.pdf;

import com.vitality.ai.ocr.service.image.ImageProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class PdfboxRenderer implements PdfRenderer {
    @Override
    public List<byte[]> renderPagesToImages(byte[] pdfBytes, int dpi) {
        return renderPagesToImages(pdfBytes, dpi, (ImageProcessor) null);
    }

    @Override
    public List<byte[]> renderPagesToImages(byte[] pdfBytes, int dpi, ImageProcessor... imageProcessors) {
        List<byte[]> pages = new ArrayList<>();

        log.info("Rendering PDF (size: {}) to raster images ({} dpi)",
            FileUtils.byteCountToDisplaySize(pdfBytes.length), dpi);

        long start = System.currentTimeMillis();

        try (PDDocument document = Loader.loadPDF(pdfBytes)) {
            for (int i = 0; i < document.getNumberOfPages(); i++) {
                long pageStart = System.currentTimeMillis();

                byte[] imageBytes = renderPageToImage(document, i, dpi);

                imageBytes = processImage(imageBytes, imageProcessors);

                log.info("Rendered page {} of {} in {} ms",
                    i + 1, document.getNumberOfPages(), System.currentTimeMillis() - pageStart);

                pages.add(imageBytes);
            }
        } catch (Exception e) {
            throw new PdfRenderException("Failed to render PDF", e);
        }

        long totalSize = pages.stream().mapToLong(page -> page.length).sum();
        log.info("Rendered {} pages of total size {} in {} ms",
            pages.size(), FileUtils.byteCountToDisplaySize(totalSize), System.currentTimeMillis() - start);

        return pages;
    }

    private static byte[] processImage(byte[] imageBytes, ImageProcessor[] imageProcessors) {
        byte[] processedImage = imageBytes;
        if (imageProcessors != null) {
            for (ImageProcessor processor : imageProcessors) {
                if (processor != null) {
                    processedImage = processor.process(processedImage);
                }
            }
        }
        return processedImage;
    }

    private byte[] renderPageToImage(PDDocument document, int pageIndex, int dpi) throws IOException {
        if (pageIndex < 0 || pageIndex >= document.getNumberOfPages()) {
            throw new IllegalArgumentException("Page index out of range: " + pageIndex);
        }
        PDFRenderer renderer = new PDFRenderer(document);
        BufferedImage image = renderer.renderImageWithDPI(pageIndex, dpi);
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ImageIO.write(image, "png", out);
        return out.toByteArray();
    }
}
