package com.vitality.ai.ocr.controller;

import com.vitality.ai.ocr.model.document.ScannedDocumentResponse;
import com.vitality.ai.ocr.service.scan.AiDocumentScanner;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@Slf4j
@RestController
@RequestMapping("/api/document/scan")
@RequiredArgsConstructor
public class DocumentScannerController {

    private final AiDocumentScanner documentScanner;

    @PostMapping(path = "/assessment", consumes = "multipart/form-data")
    public ResponseEntity<ScannedDocumentResponse> scanAssessment(
        @RequestParam(value = "promptId", defaultValue = "hra-v2") String promptId,
        @RequestParam("file") MultipartFile file) throws IOException {

        ScannedDocumentResponse response = documentScanner.scanDocument(promptId, file.getBytes(), file.getContentType());

        return ResponseEntity.ok(response);
    }
}
