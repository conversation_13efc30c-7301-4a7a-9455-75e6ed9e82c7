package com.vitality.ai.ocr.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitality.ai.ocr.service.FileValidationService;
import com.vitality.ai.ocr.service.pdf.PdfConverter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

/**
 * REST Controller for converting regular PDFs to "scanned" PDFs.
 * This controller accepts PDF files and returns them as image-based PDFs,
 * simulating the effect of physically scanning a document.
 */
@RestController
@RequestMapping("/api/pdf/convert")
@Slf4j
@Tag(name = "PDF Converter", description = "Convert regular PDFs to scanned PDFs")
public class PdfConverterController extends BaseRestController {

    private final PdfConverter pdfConverter;
    private final FileValidationService fileValidationService;

    public PdfConverterController(PdfConverter pdfConverter, FileValidationService fileValidationService,
                                 ObjectMapper objectMapper) {
        super(objectMapper);
        this.pdfConverter = pdfConverter;
        this.fileValidationService = fileValidationService;
    }

    /**
     * Convert a regular PDF to a "scanned" PDF where each page is rendered as an image.
     * This is useful for testing OCR services or document processing workflows that expect scanned documents.
     *
     * @param file The PDF file to convert
     * @param dpi  The resolution for rendering pages (optional, default: 300)
     * @return The converted "scanned" PDF as a downloadable file
     */
    @Operation(
        summary = "Convert PDF to Scanned PDF",
        description = "Converts a regular PDF document into a 'scanned' PDF where each page is rendered as a high-resolution image. " +
            "This simulates the effect of physically scanning a document, making it useful for testing OCR services " +
            "or document processing workflows that expect image-based PDFs."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "PDF successfully converted to scanned format",
            content = @Content(
                mediaType = "application/pdf",
                schema = @Schema(type = "string", format = "binary", description = "Scanned PDF file")
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid input - file is empty, not a PDF, or invalid DPI",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Map.class)
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error during PDF conversion",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Map.class)
            )
        )
    })
    @PostMapping(value = "/scanned", consumes = "multipart/form-data", produces = "application/pdf")
    public ResponseEntity<Resource> convertToScannedPdf(
        @Parameter(
            description = "PDF file to convert to scanned format",
            required = true,
            content = @Content(mediaType = "application/pdf")
        )
        @RequestParam("file") MultipartFile file,

        @Parameter(
            description = "Resolution for rendering pages in DPI (dots per inch). " +
                "Higher values produce better quality but larger files. " +
                "Minimum: 150, Recommended: 300, Maximum: 600",
            example = "300"
        )
        @RequestParam(value = "dpi", defaultValue = "300") int dpi) {

        log.info("Converting PDF: {} (size: {} bytes) to a scanned PDF at {} DPI",
            file.getOriginalFilename(), file.getSize(), dpi);

        try {
            validateDpi(dpi);
            fileValidationService.validatePdfFile(file);

            byte[] scannedPdfBytes = pdfConverter.convertToScannedPdf(file.getBytes(), dpi);

            // Prepare response
            String originalFilename = file.getOriginalFilename();
            String outputFilename = generateOutputFilename(originalFilename);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", outputFilename);

            return ResponseEntity.ok()
                .headers(headers)
                .body(new ByteArrayResource(scannedPdfBytes));

        } catch (IllegalArgumentException e) {
            log.warn("Invalid input for PDF conversion: {}", e.getMessage());
            return createErrorResponse("Invalid input: " + e.getMessage(), HttpStatus.BAD_REQUEST.value());

        } catch (IOException e) {
            log.error("IO error during PDF conversion: {}", e.getMessage(), e);
            return createErrorResponse("Failed to process PDF: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR.value());

        } catch (Exception e) {
            log.error("Unexpected error during PDF conversion: {}", e.getMessage(), e);
            return createErrorResponse("Internal server error during PDF conversion", HttpStatus.INTERNAL_SERVER_ERROR.value());
        }
    }

    /**
     * Generate output filename based on input filename.
     */
    private String generateOutputFilename(String originalFilename) {
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            return "scanned_document.pdf";
        }

        String baseName = originalFilename;
        if (baseName.toLowerCase().endsWith(".pdf")) {
            baseName = baseName.substring(0, baseName.length() - 4);
        }

        return "scanned_" + baseName + ".pdf";
    }


}
