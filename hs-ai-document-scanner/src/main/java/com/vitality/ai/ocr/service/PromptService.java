package com.vitality.ai.ocr.service;

import com.vitality.ai.ocr.config.PromptProperties;
import com.vitality.ai.ocr.model.prompt.Prompt;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
@RequiredArgsConstructor
public class PromptService {

    private final PromptProperties promptProperties;
    private final Map<String, Prompt> promptCache = new ConcurrentHashMap<>();

    @PostConstruct
    public void initializeCache() {
        if (promptProperties.getPrompts() == null) {
            return;
        }
        
        promptProperties.getPrompts().forEach((name, definition) -> {
            Prompt prompt = Prompt.builder()
                .withRole(definition.getRole())
                .withTask(definition.getTask());

            if (definition.getContext() != null) {
                for (Map.Entry<String, List<String>> entry : definition.getContext().entrySet()) {
                    prompt.withContextSection(entry.getKey(), entry.getValue());
                }
            }

            if (definition.getInstructions() != null) {
                for (Map.Entry<String, List<String>> entry : definition.getInstructions().entrySet()) {
                    prompt.withInstructionsBlock(entry.getKey(), entry.getValue());
                }
            }

            promptCache.put(name, prompt);
        });
    }

    public Prompt getPrompt(String promptName) {
        return promptCache.get(promptName);
    }

    public Map<String, Prompt> getAllPrompts() {
        return Collections.unmodifiableMap(promptCache);
    }
}
