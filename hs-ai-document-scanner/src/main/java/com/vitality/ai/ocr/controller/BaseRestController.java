package com.vitality.ai.ocr.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
public abstract class BaseRestController {

    protected final ObjectMapper objectMapper;

    protected ResponseEntity<Resource> createErrorResponse(String message, int statusCode) {
        return ResponseEntity.status(statusCode)
            .contentType(MediaType.APPLICATION_JSON)
            .body(new ByteArrayResource(createErrorJson(message).getBytes()));
    }

    protected String createErrorJson(String message) {
        try {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", message);
            errorResponse.put("timestamp", Instant.now().toString());
            return objectMapper.writeValueAsString(errorResponse);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize error response: {}", e.getMessage(), e);
            return String.format("{\"error\": \"%s\", \"timestamp\": \"%s\"}",
                message.replace("\"", "\\\""), Instant.now().toString());
        }
    }

    protected void validateDpi(int dpi) {
        if (dpi < 150) {
            throw new IllegalArgumentException("DPI must be at least 150 for readable output");
        }
        if (dpi > 600) {
            log.warn("High DPI ({}) may result in very large file sizes and slow processing", dpi);
        }
    }
}
