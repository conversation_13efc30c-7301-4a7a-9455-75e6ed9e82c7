package com.vitality.ai.ocr.service.image;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.ByteLookupTable;
import java.awt.image.DataBuffer;
import java.awt.image.LookupOp;
import java.awt.image.Raster;
import java.awt.image.WritableRaster;
import java.util.Arrays;
import java.util.Random;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class ImageTransformations {

    private static final Random random = new Random();

    /**
     * Performs contrast stretching (histogram normalization) by stretching pixel intensities
     * between the image's min and max brightness.
     */
    public static BufferedImage stretchContrast(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        WritableRaster in = image.getRaster();

        // Use a histogram for faster min/max calculation
        int[] histogram = new int[256];
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                histogram[in.getSample(x, y, 0)]++;
            }
        }

        // Find min/max values from histogram
        int min = 0;
        while (min < 255 && histogram[min] == 0) min++;

        int max = 255;
        while (max > 0 && histogram[max] == 0) max--;

        // Skip if no stretch needed
        if (min == 0 && max == 255) {
            return image;
        }

        // Prepare lookup table for faster processing
        byte[] lut = new byte[256];
        if (max > min) {
            float scale = 255f / (max - min);
            for (int i = 0; i < 256; i++) {
                lut[i] = (byte) (i <= min ? 0 : (i >= max ? 255 : Math.round((i - min) * scale)));
            }
        } else {
            // Edge case: all pixels same value
            Arrays.fill(lut, (byte) 128);
        }

        // Apply lookup table transform
        LookupOp op = new LookupOp(new ByteLookupTable(0, lut), null);
        return op.filter(image, null);
    }

    /**
     * Applies a 3x3 median filter to denoise the grayscale image.
     * Pure Java implementation using fast sorting.
     */
    public static BufferedImage denoise(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage out = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);
        WritableRaster inRaster = image.getRaster();
        WritableRaster outRaster = out.getRaster();
        int[] medianBuffer = new int[9];

        // Skip edges to avoid bounds checking
        for (int y = 1; y < height - 1; y++) {
            for (int x = 1; x < width - 1; x++) {
                int idx = 0;
                for (int dy = -1; dy <= 1; dy++) {
                    for (int dx = -1; dx <= 1; dx++) {
                        medianBuffer[idx++] = inRaster.getSample(x + dx, y + dy, 0);
                    }
                }

                // Fast median of 9 elements - partial sort
                for (int i = 0; i < 5; i++) {
                    int min_idx = i;
                    for (int j = i + 1; j < 9; j++) {
                        if (medianBuffer[j] < medianBuffer[min_idx]) {
                            min_idx = j;
                        }
                    }
                    // Swap
                    if (min_idx != i) {
                        int temp = medianBuffer[i];
                        medianBuffer[i] = medianBuffer[min_idx];
                        medianBuffer[min_idx] = temp;
                    }
                }

                outRaster.setSample(x, y, 0, medianBuffer[4]);
            }
        }

        // Copy edges from original
        for (int y = 0; y < height; y++) {
            outRaster.setSample(0, y, 0, inRaster.getSample(0, y, 0));
            outRaster.setSample(width - 1, y, 0, inRaster.getSample(width - 1, y, 0));
        }
        for (int x = 1; x < width - 1; x++) {
            outRaster.setSample(x, 0, 0, inRaster.getSample(x, 0, 0));
            outRaster.setSample(x, height - 1, 0, inRaster.getSample(x, height - 1, 0));
        }

        return out;
    }

    /**
     * Adds realistic scanning noise with default light noise level.
     */
    public static BufferedImage addNoise(BufferedImage image) {
        return addNoise(image, 0.8);
    }

    /**
     * Adds realistic scanning noise to simulate scanned document artifacts.
     *
     * @param image      the grayscale image to add noise to
     * @param noiseLevel noise intensity (0.0-1.0), where 0.1 is light noise, 0.5 is moderate
     */
    public static BufferedImage addNoise(BufferedImage image, double noiseLevel) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage out = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);
        WritableRaster inRaster = image.getRaster();
        WritableRaster outRaster = out.getRaster();

        // Copy original image first
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                outRaster.setSample(x, y, 0, inRaster.getSample(x, y, 0));
            }
        }

        addUnevenLighting(outRaster, width, height, noiseLevel * 0.8, random);

        addDustSpots(outRaster, width, height, noiseLevel * 0.6, random);

        addScanningLines(outRaster, width, height, noiseLevel * 0.4, random);

        addCompressionNoise(outRaster, width, height, noiseLevel * 0.7, random);

        return out;
    }

    private static void addUnevenLighting(WritableRaster raster, int w, int h, double intensity, Random random) {
        double centerX = w / 2.0;
        double centerY = h / 2.0;
        double maxDist = Math.sqrt(centerX * centerX + centerY * centerY);

        for (int y = 0; y < h; y++) {
            for (int x = 0; x < w; x++) {
                double dist = Math.sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
                double factor = 1.0 - (dist / maxDist) * intensity * 0.4;
                factor += (random.nextGaussian() * 0.08 * intensity);

                int pixel = raster.getSample(x, y, 0);
                pixel = (int) Math.max(0, Math.min(255, pixel * factor));
                raster.setSample(x, y, 0, pixel);
            }
        }
    }

    private static void addPaperTexture(WritableRaster raster, int w, int h, double intensity, Random random) {
        for (int y = 0; y < h; y++) {
            for (int x = 0; x < w; x++) {
                double noise = (random.nextGaussian() * 8 + Math.sin(x * 0.05) * Math.cos(y * 0.05) * 4) * intensity;
                int pixel = raster.getSample(x, y, 0);
                pixel = (int) Math.max(0, Math.min(255, pixel + noise));
                raster.setSample(x, y, 0, pixel);
            }
        }
    }

    private static void addDustSpots(WritableRaster raster, int w, int h, double intensity, Random random) {
        int numSpots = (int) (w * h * intensity * 0.0001);

        for (int i = 0; i < numSpots; i++) {
            int spotX = random.nextInt(w);
            int spotY = random.nextInt(h);
            int spotSize = random.nextInt(5) + 1; // Increased max size for more variety

            // Calculate transparency based on spot size - larger spots are more transparent
            double transparency = Math.min(0.9, 0.3 + (spotSize - 1) * 0.15);
            int spotIntensity = 50 + random.nextInt(100); // Base dust color

            for (int dy = -spotSize; dy <= spotSize; dy++) {
                for (int dx = -spotSize; dx <= spotSize; dx++) {
                    int x = spotX + dx, y = spotY + dy;
                    if (x >= 0 && x < w && y >= 0 && y < h) {
                        double distance = Math.sqrt(dx * dx + dy * dy);
                        if (distance <= spotSize) {
                            // Apply distance-based falloff for more natural spots
                            double falloff = 1.0 - (distance / spotSize);
                            double effectiveTransparency = transparency * falloff;

                            int originalPixel = raster.getSample(x, y, 0);
                            int blendedPixel = (int) (originalPixel * (1 - effectiveTransparency) +
                                spotIntensity * effectiveTransparency);
                            raster.setSample(x, y, 0, Math.clamp(blendedPixel, 0, 255));
                        }
                    }
                }
            }
        }
    }

    private static void addScanningLines(WritableRaster raster, int w, int h, double intensity, Random random) {
        // Add horizontal scanning artifacts
        int numLines = (int) (h * intensity * 0.1);
        for (int i = 0; i < numLines; i++) {
            int lineY = random.nextInt(h);
            int lineIntensity = (int) (random.nextGaussian() * 40 * intensity + (random.nextBoolean() ? 20 : -20));

            for (int x = 0; x < w; x++) {
                int pixel = raster.getSample(x, lineY, 0);
                pixel = Math.clamp(pixel + lineIntensity, 0, 255);
                raster.setSample(x, lineY, 0, pixel);
            }
        }

        // Add vertical scanning streaks
        int numVertLines = (int) (w * intensity * 0.03);
        for (int i = 0; i < numVertLines; i++) {
            int lineX = random.nextInt(w);
            int lineIntensity = (int) (random.nextGaussian() * 30 * intensity + (random.nextBoolean() ? 15 : -15));

            for (int y = 0; y < h; y++) {
                int pixel = raster.getSample(lineX, y, 0);
                pixel = Math.clamp(pixel + lineIntensity, 0, 255);
                raster.setSample(lineX, y, 0, pixel);
            }
        }

        // Add occasional vertical streaks
        numVertLines = (int) (w * intensity * 0.02);
        for (int i = 0; i < numVertLines; i++) {
            int lineX = random.nextInt(w);
            int lineIntensity = (int) (random.nextGaussian() * 20 * intensity);

            for (int y = 0; y < h; y++) {
                int pixel = raster.getSample(lineX, y, 0);
                pixel = Math.clamp(pixel + lineIntensity, 0, 255);
                raster.setSample(lineX, y, 0, pixel);
            }
        }
    }

    private static void addCompressionNoise(WritableRaster raster, int w, int h, double intensity, Random random) {
        for (int blockY = 0; blockY < h; blockY += 8) {
            for (int blockX = 0; blockX < w; blockX += 8) {
                if (random.nextDouble() < intensity * 0.3) {
                    int blockNoise = (int) (random.nextGaussian() * 12 * intensity);

                    for (int y = blockY; y < Math.min(blockY + 8, h); y++) {
                        for (int x = blockX; x < Math.min(blockX + 8, w); x++) {
                            int pixel = raster.getSample(x, y, 0);
                            pixel = Math.clamp(pixel + blockNoise, 0, 255);
                            raster.setSample(x, y, 0, pixel);
                        }
                    }
                }
            }
        }
    }

    public static BufferedImage toGrayscale(BufferedImage src) {
        if (src.getType() == BufferedImage.TYPE_BYTE_GRAY) {
            return src;
        }
        BufferedImage gray = new BufferedImage(src.getWidth(), src.getHeight(), BufferedImage.TYPE_BYTE_GRAY);
        Graphics2D g = gray.createGraphics();
        g.drawImage(src, 0, 0, null);
        g.dispose();
        return gray;
    }

    /**
     * Grades an image by evaluating brightness, contrast, noise, and uneven lighting.
     * Uses optimized batch sampling and avoids pixel-by-pixel loops.
     */
    public static ImageTransformations.Grade gradeImage(BufferedImage gray) {
        int w = gray.getWidth(), h = gray.getHeight();

        // Use DataBuffer directly for faster pixel access
        DataBuffer buffer = gray.getRaster().getDataBuffer();
        int numPixels = w * h;

        // Calculate using single pass algorithm
        long sum = 0, sumSq = 0;
        for (int i = 0; i < numPixels; i++) {
            int p = buffer.getElem(i);
            sum += p;
            sumSq += (long) p * p;
        }

        double mean = sum / (double) numPixels;
        double variance = (sumSq - ((double) sum * sum) / numPixels) / numPixels;
        double stdDev = Math.sqrt(variance);

        Raster raster = gray.getRaster();
        boolean uneven = checkLightingFast(raster, w, h, mean);
        int noise = estimateNoiseFast(raster, w, h);

        return new ImageTransformations.Grade((int) mean, (int) stdDev, uneven, noise);
    }

    /**
     * Detects uneven lighting across 4 corner blocks using mean pixel intensity and checks variation.
     */
    private static boolean checkLightingFast(Raster raster, int w, int h, double globalMean) {
        int blockSize = Math.min(16, Math.min(w, h) / 8); // Adaptive block size
        int[][] blocks = new int[4][];
        blocks[0] = getBlockSamples(raster, 0, 0, blockSize);
        blocks[1] = getBlockSamples(raster, w - blockSize, 0, blockSize);
        blocks[2] = getBlockSamples(raster, 0, h - blockSize, blockSize);
        blocks[3] = getBlockSamples(raster, w - blockSize, h - blockSize, blockSize);
        double[] means = Arrays.stream(blocks).mapToDouble(ImageTransformations::meanOf).toArray();
        double min = Arrays.stream(means).min().orElse(globalMean);
        double max = Arrays.stream(means).max().orElse(globalMean);
        return (max - min) > globalMean * 0.25;
    }

    /**
     * Estimates noise by measuring gradient differences on a fixed pixel stride grid.
     * High differences imply speckle noise or blur artifacts.
     */
    private static int estimateNoiseFast(Raster raster, int w, int h) {
        // Adaptive stride based on image size
        int stride = Math.max(1, Math.min(w, h) / 100);
        int count = 0;
        int[] neighborhood = new int[5]; // Center + 4 neighbors

        for (int y = stride; y < h - stride; y += stride) {
            for (int x = stride; x < w - stride; x += stride) {
                neighborhood[0] = raster.getSample(x, y, 0);      // Center
                neighborhood[1] = raster.getSample(x - stride, y, 0);  // Left
                neighborhood[2] = raster.getSample(x + stride, y, 0);  // Right
                neighborhood[3] = raster.getSample(x, y - stride, 0);  // Top
                neighborhood[4] = raster.getSample(x, y + stride, 0);  // Bottom

                int center = neighborhood[0];
                int diff = Math.abs(center - neighborhood[1]) +
                    Math.abs(center - neighborhood[2]) +
                    Math.abs(center - neighborhood[3]) +
                    Math.abs(center - neighborhood[4]);

                if (diff > 100) count++;
            }
        }
        return count;
    }

    /**
     * Extracts a square block of pixels from the raster at the given position.
     */
    private static int[] getBlockSamples(Raster r, int x, int y, int size) {
        int[] samples = new int[size * size];
        r.getSamples(x, y, size, size, 0, samples);
        return samples;
    }

    /**
     * Computes the mean (average) of an int array.
     */
    private static double meanOf(int[] data) {
        long sum = 0;
        for (int value : data) {
            sum += value;
        }
        return sum / (double) data.length;
    }

    /**
     * Detects skew angle by using a more efficient algorithm that samples the image
     * at key points instead of doing full rotations.
     */
    public static double detectSkewAngle(BufferedImage binary) {
        int w = binary.getWidth(), h = binary.getHeight();
        Raster raster = binary.getRaster();

        // For small images, use simpler Hough transform approach
        if (w * h < 1000000) {
            return detectSkewHough(binary);
        }

        double bestAngle = 0;
        double bestScore = Double.MIN_VALUE;

        // Use fewer angles for better performance
        for (double angle = -5; angle <= 5; angle += 0.5) {
            // Project image at this angle and measure variance
            double[] projection = projectAtAngle(raster, w, h, angle);
            double variance = computeVariance(projection);

            if (variance > bestScore) {
                bestScore = variance;
                bestAngle = angle;
            }
        }

        return bestAngle;
    }

    /**
     * Uses Hough transform to detect lines and determine skew angle.
     * More efficient for smaller images.
     */
    private static double detectSkewHough(BufferedImage binary) {
        int w = binary.getWidth(), h = binary.getHeight();
        Raster raster = binary.getRaster();

        // Sample the image for edge points
        int sampleStride = Math.max(1, Math.min(w, h) / 100);
        int maxCount = 0;
        double bestAngle = 0;

        // Simple angle histogram (10 degree buckets)
        int[] angleHistogram = new int[36];

        for (int y = sampleStride; y < h - sampleStride; y += sampleStride) {
            for (int x = sampleStride; x < w - sampleStride; x += sampleStride) {
                // Check if this is an edge point
                if (isEdgePoint(raster, x, y)) {
                    // Calculate gradient direction
                    double gradientAngle = calculateGradientAngle(raster, x, y);
                    int bucketIndex = (int) ((gradientAngle + 180) / 10) % 36;
                    angleHistogram[bucketIndex]++;

                    if (angleHistogram[bucketIndex] > maxCount) {
                        maxCount = angleHistogram[bucketIndex];
                        bestAngle = (bucketIndex * 10) - 180;
                    }
                }
            }
        }

        // Convert the dominant angle to skew angle
        return (bestAngle + 90) % 180 - 90;
    }

    /**
     * Projects the image at a specific angle and returns the projection.
     */
    private static double[] projectAtAngle(Raster raster, int w, int h, double angleDegrees) {
        double radians = Math.toRadians(angleDegrees);
        double sinTheta = Math.sin(radians);
        double cosTheta = Math.cos(radians);

        // Calculate projection size
        int diag = (int) Math.ceil(Math.sqrt(w * w + h * h));
        double[] projection = new double[diag * 2];

        // Sample the image
        int stride = Math.max(1, Math.min(w, h) / 100);
        for (int y = 0; y < h; y += stride) {
            for (int x = 0; x < w; x += stride) {
                if (raster.getSample(x, y, 0) == 0) { // Black pixel
                    // Project point
                    int projIndex = (int) (x * cosTheta + y * sinTheta + diag);
                    if (projIndex >= 0 && projIndex < projection.length) {
                        projection[projIndex]++;
                    }
                }
            }
        }

        return projection;
    }

    /**
     * Determines if a point is an edge point by checking neighbors.
     */
    private static boolean isEdgePoint(Raster raster, int x, int y) {
        int center = raster.getSample(x, y, 0);

        // Check if any neighboring pixel is different
        for (int dy = -1; dy <= 1; dy++) {
            for (int dx = -1; dx <= 1; dx++) {
                if (dx == 0 && dy == 0) continue;
                if (raster.getSample(x + dx, y + dy, 0) != center) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Calculates the gradient angle at a point using Sobel operator.
     */
    private static double calculateGradientAngle(Raster raster, int x, int y) {
        // Sobel operators
        int gx = (-1 * raster.getSample(x - 1, y - 1, 0)) + (-2 * raster.getSample(x - 1, y, 0)) + (-1 * raster.getSample(x - 1, y + 1, 0)) +
            (1 * raster.getSample(x + 1, y - 1, 0)) + (2 * raster.getSample(x + 1, y, 0)) + (1 * raster.getSample(x + 1, y + 1, 0));

        int gy = (-1 * raster.getSample(x - 1, y - 1, 0)) + (-2 * raster.getSample(x, y - 1, 0)) + (-1 * raster.getSample(x + 1, y - 1, 0)) +
            (1 * raster.getSample(x - 1, y + 1, 0)) + (2 * raster.getSample(x, y + 1, 0)) + (1 * raster.getSample(x + 1, y + 1, 0));

        return Math.toDegrees(Math.atan2(gy, gx));
    }

    /**
     * Rotates a binary image using Graphics2D with bilinear interpolation and
     * white background fill.
     */
    public static BufferedImage rotateImage(BufferedImage src, double angleDegrees) {
        // Skip rotation if angle is too small
        if (Math.abs(angleDegrees) < 0.05) {
            return src;
        }

        double radians = Math.toRadians(angleDegrees);
        int w = src.getWidth(), h = src.getHeight();
        double sin = Math.abs(Math.sin(radians)), cos = Math.abs(Math.cos(radians));
        int newW = (int) Math.floor(w * cos + h * sin);
        int newH = (int) Math.floor(h * cos + w * sin);

        BufferedImage rotated = new BufferedImage(newW, newH, BufferedImage.TYPE_BYTE_BINARY);
        Graphics2D g2d = rotated.createGraphics();

        // Use nearest neighbor for binary images (better for OCR)
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION,
            RenderingHints.VALUE_INTERPOLATION_NEAREST_NEIGHBOR);
        g2d.setBackground(Color.WHITE);
        g2d.clearRect(0, 0, newW, newH);
        g2d.translate(newW / 2.0, newH / 2.0);
        g2d.rotate(radians);
        g2d.translate(-w / 2.0, -h / 2.0);
        g2d.drawImage(src, 0, 0, null);
        g2d.dispose();

        return rotated;
    }

    /**
     * Scales an image to a specified percentage of its original size.
     *
     * @param image        The image to scale
     * @param scalePercent The percentage to scale (50 = half size, 200 = double size)
     * @return The scaled image
     */
    public static BufferedImage scaleImage(BufferedImage image, int scalePercent) {
        if (scalePercent <= 0) throw new IllegalArgumentException("Scale percentage must be positive");
        if (scalePercent == 100) return image; // No scaling needed

        int targetWidth = (int) (image.getWidth() * scalePercent / 100.0);
        int targetHeight = (int) (image.getHeight() * scalePercent / 100.0);

        // Use faster nearest neighbor scaling for binary images (better for OCR text)
        // This preserves the sharp edges of text better than bilinear
        Object hint = RenderingHints.VALUE_INTERPOLATION_NEAREST_NEIGHBOR;

        BufferedImage scaled = new BufferedImage(targetWidth, targetHeight, image.getType());
        Graphics2D g = scaled.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, hint);
        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_OFF);
        g.drawImage(image, 0, 0, targetWidth, targetHeight, null);
        g.dispose();

        return scaled;
    }

    /**
     * Computes variance of an array (used to measure horizontal alignment).
     */
    private static double computeVariance(double[] values) {
        double sum = 0, sumSq = 0;

        for (double v : values) {
            sum += v;
            sumSq += v * v;
        }

        double mean = sum / values.length;
        return (sumSq / values.length) - (mean * mean);
    }

    @Data
    @AllArgsConstructor
    public static class Grade {
        private int brightness;
        private int contrast;
        private boolean hasUnevenLighting;
        private int noiseLevel;
    }
}




















