package com.vitality.ai.ocr.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.vitality.ai.ocr.model.document.ScannedDocumentResponse;
import com.vitality.ai.ocr.service.json.SchemaGenerator;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/schema")
public class SchemaController {

    private final SchemaGenerator schemaGenerator;

    public SchemaController(SchemaGenerator schemaGenerator) {
        this.schemaGenerator = schemaGenerator;
    }

    @GetMapping("/scanned-document-response")
    public ResponseEntity<JsonNode> getScannedDocumentResponseSchema() {
        try {
            JsonNode schema = schemaGenerator.generateSchema(ScannedDocumentResponse.class);
            return ResponseEntity.ok(schema);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }
}
