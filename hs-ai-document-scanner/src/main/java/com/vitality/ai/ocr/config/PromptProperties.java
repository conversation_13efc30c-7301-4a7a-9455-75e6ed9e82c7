package com.vitality.ai.ocr.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Component
@ConfigurationProperties(prefix = "ai")
public class PromptProperties {
    private Map<String, PromptDefinition> prompts;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PromptDefinition {
        private String role;
        private Map<String, List<String>> context;
        private String task;
        private Map<String, List<String>> instructions;
    }
}
