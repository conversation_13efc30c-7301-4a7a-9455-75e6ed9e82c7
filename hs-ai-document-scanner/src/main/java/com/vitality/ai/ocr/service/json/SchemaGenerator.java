package com.vitality.ai.ocr.service.json;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.module.jsonSchema.JsonSchema;
import com.fasterxml.jackson.module.jsonSchema.JsonSchemaGenerator;
import org.springframework.stereotype.Component;

@Component
public class SchemaGenerator {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final JsonSchemaGenerator schemaGen = new JsonSchemaGenerator(objectMapper);

    public JsonNode generateSchema(Class<?> clazz) {
        try {
            JsonSchema schema = schemaGen.generateSchema(clazz);
            return objectMapper.valueToTree(schema);
        } catch (Exception e) {
            throw new JsonSchemaGeneratorException("Error generating JSON schema", e);
        }
    }

    public String generateSchemaAsString(Class<?> clazz) {
        try {
            return objectMapper.writerWithDefaultPrettyPrinter()
                .writeValueAsString(generateSchema(clazz));
        } catch (Exception e) {
            throw new JsonSchemaGeneratorException("Error generating JSON schema", e);
        }
    }
}
