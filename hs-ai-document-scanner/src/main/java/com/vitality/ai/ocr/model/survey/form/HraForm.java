package com.vitality.ai.ocr.model.survey.form;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.core.io.ClassPathResource;

public class HraForm {

    public static FormTemplate getHraTemplate() {
        ClassPathResource classPathResource = new ClassPathResource("./sample/hraQuesions.json");
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_DEFAULT);

        try {
            return objectMapper.readValue(classPathResource.getInputStream(), FormTemplate.class);
        } catch (Exception e) {
            throw new RuntimeException("Failed to load HRA template", e);
        }
    }

}
