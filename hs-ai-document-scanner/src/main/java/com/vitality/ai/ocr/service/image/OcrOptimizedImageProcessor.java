package com.vitality.ai.ocr.service.image;

import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Slf4j
public class OcrOptimizedImageProcessor implements ImageProcessor {
    private static final int scalePercent = 100;

    @Override
    public byte[] process(byte[] inputBytes) {
        int imageNumber = 0;

        try {
            BufferedImage original = ImageIO.read(new ByteArrayInputStream(inputBytes));
            if (original == null) {
                throw new IllegalArgumentException("Invalid image");
            }

            BufferedImage gray = ImageTransformations.toGrayscale(original);
            ImageTransformations.Grade grade = ImageTransformations.gradeImage(gray);
            performDebugSave(imageNumber, gray, "gray"); // Save grayscale image for debugging
            // Apply pipeline based on image quality
            if (grade.getNoiseLevel() > 5) {
                gray = ImageTransformations.denoise(gray);
                performDebugSave(imageNumber, gray, "denoise"); // Save grayscale image for debugging
            }

            if (grade.getContrast() < 30 || grade.getBrightness() < 80 || grade.getBrightness() > 200) {
                gray = ImageTransformations.stretchContrast(gray);
                performDebugSave(imageNumber, gray, "contrast"); // Save contrast-stretched image for debugging
            }

            BufferedImage binary = gray;//grade.hasUnevenLighting
//                ? adaptiveBinarizeParallel(gray, 15, 10)
//                : binarize(gray, 128);

            performDebugSave(imageNumber, binary, "binarize"); // Save binary image for debugging
            // Enhance font blackness for better OCR
//        binary = enhanceFontBlackness(binary);
//        performDebugSave(imageNumber,binary,"font"); // Save enhanced binary image for debugging

            double skewAngle = ImageTransformations.detectSkewAngle(binary);
            if (Math.abs(skewAngle) > 0.5) {
                binary = ImageTransformations.rotateImage(binary, -skewAngle); // Deskew
                performDebugSave(imageNumber, binary, "rotate"); // Save deskewed image for debugging
            }

            // Scale as the final step (moved from beginning to end)
            if (scalePercent != 100) {
                binary = ImageTransformations.scaleImage(binary, scalePercent);
                performDebugSave(imageNumber, binary, "scale"); // Save scaled image for debugging
            }

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            ImageIO.write(binary, "png", out);

            return out.toByteArray();
        } catch (Exception e) {
            throw new ImageProcessorException("Failed to process image", e);
        }
    }

    public static void performDebugSave(int imageNumber, BufferedImage bufferedImage, String name) {
        if (log.isDebugEnabled()) {
            try {
                Path debugDir = Paths.get("/Users/<USER>/development/research/chatAgent/hs-document-scanner/src/main/resources/debug");
                if (!Files.exists(debugDir)) {
                    Files.createDirectories(debugDir);
                }

                String timestamp = imageNumber + "";
                String extension = "png";
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                ImageIO.write(bufferedImage, "png", out);
                Path imageFile = debugDir.resolve(timestamp + "_" + name + "_debug_image" + "." + extension);
                Files.write(imageFile, out.toByteArray());
                log.info("Debug image saved to: {}", imageFile.toAbsolutePath());

            } catch (IOException e) {
                log.error("Failed to save debug image file", e);
            }
        }
    }
}
