package com.vitality.ai.ocr.model.prompt;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
public class Prompt {
    private String role;
    private String task;
    private final Map<String, List<String>> context = new LinkedHashMap<>();
    private final Map<String, List<String>> instructions = new LinkedHashMap<>();

    public static Prompt builder() {
        return new Prompt();
    }

    public Prompt withRole(String role) {
        this.role = role;
        return this;
    }

    public Prompt withContextSection(String name, List<String> content) {
        this.context.put(name, content);
        return this;
    }

    public Prompt withTask(String task) {
        this.task = task;
        return this;
    }

    public Prompt withInstructionsBlock(String title, List<String> instructions) {
        this.instructions.put(title, instructions);
        return this;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();

        if (role != null && !role.isBlank()) {
            sb.append("Role: ").append(role).append("\n\n");
        }

        if (task != null && !task.isBlank()) {
            sb.append("Task: ").append(task).append("\n\n");
        }

        for (Map.Entry<String, List<String>> entry : context.entrySet()) {
            sb.append(entry.getKey()).append(":\n");
            for (String contextItem : entry.getValue()) {
                sb.append("- ").append(contextItem).append("\n");
            }
            sb.append("\n");
        }

        for (Map.Entry<String, List<String>> entry : instructions.entrySet()) {
            sb.append(entry.getKey()).append(":\n");
            for (String instruction : entry.getValue()) {
                sb.append("- ").append(instruction).append("\n");
            }
            sb.append("\n");
        }

        return sb.toString().trim();
    }
}
