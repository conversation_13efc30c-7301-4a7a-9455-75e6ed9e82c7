package com.vitality.ai.ocr.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitality.ai.ocr.model.prompt.Prompt;
import com.vitality.ai.ocr.model.prompt.PromptResponse;
import com.vitality.ai.ocr.service.PromptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.TreeMap;

@Slf4j
@RestController
@RequestMapping("/api/prompt")
public class PromptController extends BaseRestController {

    private final PromptService promptService;

    public PromptController(ObjectMapper objectMapper, PromptService promptService) {
        super(objectMapper);
        this.promptService = promptService;
    }

    @GetMapping("{promptId}")
    public ResponseEntity<PromptResponse> getPrompt(@PathVariable String promptId) {
        Prompt prompt = promptService.getPrompt(promptId);
        if (prompt == null) {
            return ResponseEntity.notFound().build();
        }

        return ResponseEntity.ok(new PromptResponse(prompt, prompt.toString()));
    }


    @GetMapping("/list")
    public ResponseEntity<Map<String, PromptResponse>> listPrompts() {
        Map<String, PromptResponse> prompts = new TreeMap<>();
        promptService.getAllPrompts().forEach((name, prompt) -> {
            prompts.put(name, new PromptResponse(prompt, prompt.toString()));
        });
        return ResponseEntity.ok(prompts);
    }

}
