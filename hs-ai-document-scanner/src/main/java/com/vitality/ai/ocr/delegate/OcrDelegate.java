package com.vitality.ai.ocr.delegate;

import com.vitality.ai.ocr.prompt.model.OcrResponse;
import com.vitality.ai.ocr.service.VitalityChatService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@Service
@RequiredArgsConstructor
public class OcrDelegate {

    private final VitalityChatService chatService;

    public OcrResponse convert(String promptName, MultipartFile fileContent) throws IOException {
        OcrResponse ocrResponse = chatService.performOcr(fileContent, promptName);
        return ocrResponse;
    }
}


