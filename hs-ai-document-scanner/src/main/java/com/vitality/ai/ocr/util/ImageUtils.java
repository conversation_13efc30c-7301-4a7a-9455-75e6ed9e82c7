package com.vitality.ai.ocr.util;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;

@Slf4j
public class ImageUtils {

    public static void performDebugSave(int imageNumber, String base64Encoded, String mimeType) {
        try {
            Path debugDir = Paths.get("/Users/<USER>/development/research/chatAgent/hs-document-scanner/src/main/resources/debug");
            if (!Files.exists(debugDir)) {
                Files.createDirectories(debugDir);
            }

            String timestamp = imageNumber + "";
            String extension = "bin";

            if (mimeType != null && mimeType.startsWith("image/")) {
                extension = mimeType.substring("image/".length()).toLowerCase();
                if (extension.equals("jpeg")) {extension = "jpg";}
                else extension = "png";
            } else {
                log.warn("Unknown or non-image mime type: {}, saving as .bin", mimeType);
            }

            byte[] decode = Base64.getDecoder().decode(base64Encoded);
            Path imageFile = debugDir.resolve("image_" + timestamp + "." + extension);
            Files.write(imageFile, decode);
            log.info("Debug image saved to: {}", imageFile.toAbsolutePath());

            Path base64File = debugDir.resolve("image_base64_" + timestamp + ".txt");
            Files.writeString(base64File, base64Encoded);
            log.info("Debug Base64 data saved to: {}", base64File.toAbsolutePath());


        } catch (IOException e) {
            log.error("Failed to save debug image file", e);
        }
    }

    public static String encodeImageToBase64(byte[] imageBytes) throws IOException {
        return Base64.getEncoder().encodeToString(imageBytes);
    }

    public static byte[] decodeImageToBase64(String imageBytes) throws IOException {
        return Base64.getDecoder().decode(imageBytes);
    }


}
