package com.vitality.ai.ocr.service.scan;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitality.ai.ocr.PromptBuilder;
import com.vitality.ai.ocr.model.document.ScannedDocumentResponse;
import com.vitality.ai.ocr.service.PromptService;
import com.vitality.ai.ocr.service.pdf.PdfConstants;
import com.vitality.ai.ocr.service.pdf.PdfRenderer;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.Content;
import dev.langchain4j.data.message.ImageContent;
import dev.langchain4j.data.message.TextContent;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.request.ChatRequest;
import dev.langchain4j.model.chat.request.ResponseFormat;
import dev.langchain4j.model.chat.request.ResponseFormatType;
import dev.langchain4j.model.chat.response.ChatResponse;
import dev.langchain4j.service.output.JsonSchemas;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class VertexAIDocumentScanner implements AiDocumentScanner {
    private final ChatModel chatModel;
    private final PdfRenderer pdfRenderer;
    private final PromptService promptService;
    private final ObjectMapper mapper;

    @Override
    public ScannedDocumentResponse scanDocument(String promptId, byte[] content, String contentType) {
        try {
            if (!contentType.toLowerCase().contains("pdf")) {
                throw new IllegalArgumentException("Unsupported content type: " + contentType);
            }

            log.info("Scanning document with prompt: {}", promptId);

            List<byte[]> pagesBytes = pdfRenderer.renderPagesToImages(content, PdfConstants.DEFAULT_DPI);

            log.info("Rendered PDF (size: {}) to raster images ({} dpi)",
                FileUtils.byteCountToDisplaySize(content.length), PdfConstants.DEFAULT_DPI);

            List<String> pagesBase64 = pagesBytes.stream().map(page -> Base64.getEncoder().encodeToString(page)).toList();

            log.info("Converted {} pages to base64", pagesBytes.size());

            String prompt = promptService.getPrompt(promptId).toString();

            log.info("Prompt: {}", prompt);

//            final SystemMessage systemMessage = SystemMessage.from(prompt);

            final ArrayList<Content> contents = new ArrayList<>();
            contents.add(new TextContent(prompt));

            pagesBase64.forEach(pageBase64 -> contents.add(new ImageContent(pageBase64, "image/png")));

            // Create user message with the image
            final UserMessage userMessage = UserMessage.from(contents);

            final List<ChatMessage> chatMessageList = List.of(userMessage);

            ChatRequest chatRequest = ChatRequest.builder()
                .messages(chatMessageList)
                .responseFormat(ResponseFormat.builder()
                    .type(ResponseFormatType.JSON)
                    .jsonSchema(JsonSchemas.jsonSchemaFrom(ScannedDocumentResponse.class).orElseThrow())
                    .build())
                .build();

            long start = System.currentTimeMillis();

            log.info("Sending request to AI model");

            ChatResponse chatResponse = chatModel.chat(chatRequest);

            log.info("Received response from AI model in {} ms", System.currentTimeMillis() - start);

            log.info(chatResponse.toString());

            return mapper.readValue(chatResponse.aiMessage().text(), ScannedDocumentResponse.class);
        } catch (Exception e) {
            throw new DocumentScannerException("Failed to parse response", e);
        }
    }

    private String getPrompt() {
//        final ObjectMapper objectMapper = new ObjectMapper();
//        objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
//        String jsonSchema = schemaGenerator.generateSchemaAsString(ScannedDocumentResponse.class);

        return PromptBuilder.create()
            .withSystemContext("You are an OCR engine that will extract text from images.")
            .withInstruction("Take the image input and extract all text from it.")
            .withInstruction("If there is an answer to a question, extract the answer from the input provided,  the question and only the ANSWERED VALUE to the value in the selectedAnswer tag.")
            .withInstruction("If there is no answer to a question, still extract the question with the value in the selectedAnswer tag as NOT ANSWERED.")
            .withInstruction("If there is any doubt about the answer, indicate which question number and the question with the value in the selectedAnswer tag as DOUBT.")
            .withInstruction("Only answered questions where the box is checked should be marked as SELECTED.")
            .withInstruction("For answers that have a checkbox, there has to be definitive selection made, a definitive selection is a marking in the box, if not then the value in the selectedAnswer tag should be NOT ANSWERED.")
            .withInstruction("You are to response with complete JSON, no short responses. Every Image is to be processed")
//            .withOutput("Structure the response as a JSON object with the following structure:" + jsonSchema)
            .build();
    }
}
