package com.vitality.ai.ocr.service.image;

import org.apache.commons.io.FileUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.random.RandomGenerator;

import static com.vitality.ai.ocr.service.image.ImageTransformations.addNoise;
import static com.vitality.ai.ocr.service.image.ImageTransformations.rotateImage;
import static com.vitality.ai.ocr.service.image.ImageTransformations.toGrayscale;

public class ScannedPageImageProcessor implements ImageProcessor {
    private static final int DEFAULT_SCALE_PERCENT = 100;

    @Override
    public byte[] process(byte[] inputBytes) {
        try {
            BufferedImage original = ImageIO.read(new ByteArrayInputStream(inputBytes));
            if (original == null) {
                throw new IllegalArgumentException("Invalid image");
            }

            BufferedImage grayscaleImage = toGrayscale(original);
//            Grade grade = gradeImage(modifiedImage);
            BufferedImage noisyImage = addNoise(grayscaleImage);

//            if (grade.contrast < 30 || grade.brightness < 80 || grade.brightness > 200) {
//                modifiedImage = stretchContrast(modifiedImage);
//            }

            double angle = RandomGenerator.getDefault().nextDouble(-0.5, 0.5);
            BufferedImage skewedImage = rotateImage(noisyImage, angle); // Deskew

//            int scalePercent = DEFAULT_SCALE_PERCENT;
//            // Scale as the final step (moved from beginning to end)
//            if (scalePercent != 100) {
//                modifiedImage = scaleImage(modifiedImage, scalePercent);
//            }

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            ImageIO.write(skewedImage, "png", out);
            return out.toByteArray();
        } catch (IOException e) {
            throw new ImageProcessorException(
                "Failed to process image of size " + FileUtils.byteCountToDisplaySize(inputBytes.length), e
            );
        }
    }

}
