package com.vitality.ai.ocr.controller;

import com.vitality.ai.ocr.delegate.OcrDelegate;
import com.vitality.ai.ocr.prompt.model.OcrResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/ocr")
@RequiredArgsConstructor
@Slf4j
public class OcrController {

    private final OcrDelegate ocrDelegate;


    @PostMapping(path = "/convert", consumes = "multipart/form-data")
    public ResponseEntity<OcrResponse> chatter(
            @RequestParam("promptId") String promptName,
            @RequestParam("file") MultipartFile file) throws IOException {

        OcrResponse convert = ocrDelegate.convert(promptName, file);

        return ResponseEntity.ok(convert);
    }

    @GetMapping("/debug/images")
    public ResponseEntity<Map<String, Object>> listDebugImages() {
        try {
            Path debugDir = Paths.get("debug");
            if (!Files.exists(debugDir)) {
                return ResponseEntity.ok(Map.of(
                        "message", "No debug directory found",
                        "images", new HashMap<>()
                ));
            }

            Map<String, String> images = new HashMap<>();
            Files.list(debugDir)
                    .filter(path -> path.toString().endsWith(".html") && path.toString().contains("image_debug_"))
                    .forEach(path -> {
                        String timestamp = path.getFileName().toString().replace("image_debug_", "").replace(".html", "");
                        images.put(timestamp, path.getFileName().toString());
                    });

            return ResponseEntity.ok(Map.of(
                    "message", "Debug images found: " + images.size(),
                    "images", images
            ));
        } catch (IOException e) {
            log.error("Error listing debug images", e);
            return ResponseEntity.internalServerError().body(Map.of(
                    "error", "Failed to list debug images: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/debug/image/{timestamp}")
    public ResponseEntity<Map<String, Object>> getDebugImage(@PathVariable String timestamp) {
        try {
            Path debugDir = Paths.get("debug");
            Path base64File = debugDir.resolve("image_base64_" + timestamp + ".txt");

            if (!Files.exists(base64File)) {
                return ResponseEntity.notFound().build();
            }

            String base64Data = Files.readString(base64File);
            Path htmlFile = debugDir.resolve("image_debug_" + timestamp + ".html");
            String htmlViewerUrl = htmlFile.getFileName().toString();

            return ResponseEntity.ok(Map.of(
                    "timestamp", timestamp,
                    "base64Data", base64Data,
                    "htmlViewer", htmlViewerUrl
            ));
        } catch (IOException e) {
            log.error("Error retrieving debug image", e);
            return ResponseEntity.internalServerError().body(Map.of(
                    "error", "Failed to retrieve debug image: " + e.getMessage()
            ));
        }
    }

}
