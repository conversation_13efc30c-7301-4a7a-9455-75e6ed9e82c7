package com.vitality.ai;

import com.vitality.ai.ocr.config.PromptProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableScheduling
@SpringBootApplication(scanBasePackages = "com.vitality.ai")
@EnableConfigurationProperties(PromptProperties.class)
public class OcrOrchestrationApplication extends SpringBootServletInitializer {

    @Override
    protected SpringApplicationBuilder configure(final SpringApplicationBuilder application) {

        return application.sources(OcrOrchestrationApplication.class);
    }

    public static void main(final String[] args) {
        SpringApplication.run(OcrOrchestrationApplication.class, args);
    }

}
