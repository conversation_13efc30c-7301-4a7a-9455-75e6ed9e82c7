package com.vitality.ai.fop.service;

import com.vitality.ai.fop.domain.FormRenderingContext;
import com.vitality.ai.fop.domain.FormStyle;
import com.vitality.ai.fop.domain.FormTemplate;
import com.vitality.ai.fop.domain.FormType;
import com.vitality.ai.fop.domain.PatientInfo;
import com.vitality.ai.fop.service.survey.SurveyTemplateProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import za.co.discovery.health.survey.domain.SurveyTemplate;

/**
 * Delegate class to coordinate form generation workflow
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FormRenderingContextFactoryImpl implements FormRenderingContextFactory {

    private final PatientService patientService;
    private final FormTemplateFactory formTemplateFactory;
    private final SurveyTemplateProvider surveyTemplateProvider;

    /**
     * Create form data for medical assessment
     *
     * @param formType  Type of form
     * @param entityNo Optional patient ID
     * @return Complete form data
     */
    @Override
    public FormRenderingContext createContext(long entityNo, FormType formType) {
        SurveyTemplate surveyTemplate = surveyTemplateProvider.getSurveyTemplate(formType.getSurveyTemplateName());
        FormTemplate formTemplate = formTemplateFactory.createTemplate(surveyTemplate);
        FormStyle style = FormStyle.getDefaultStyle();

        PatientInfo patientInfo = patientService.getPatientInfo(entityNo);

        return FormRenderingContext.builder()
            .formType(formType)
            .formTemplate(formTemplate)
            .formStyle(style)
            .patientInfo(patientInfo)
            .build();
    }
}
